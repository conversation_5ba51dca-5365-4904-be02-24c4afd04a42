#!/usr/bin/env python3
"""
Ocean Soul Sparkles - Data Cleaning Scripts
Addresses critical data quality issues identified in the migration analysis.
"""

import pandas as pd
import re
import hashlib
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataCleaner:
    """Main class for cleaning Ocean Soul Sparkles migration data."""
    
    def __init__(self, data_directory: str = "wixmigratedata/"):
        self.data_dir = data_directory
        self.cleaned_data = {}
        self.quality_report = {
            'duplicates_found': 0,
            'duplicates_resolved': 0,
            'phones_standardized': 0,
            'emails_validated': 0,
            'financial_discrepancies': 0
        }
    
    def standardize_phone_number(self, phone: str) -> Optional[str]:
        """
        Standardize phone numbers to +61 Australian format.
        Handles 7 different formats found in the data.
        """
        if pd.isna(phone) or not phone:
            return None
            
        # Clean the phone number
        phone = str(phone).strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        # Remove quotes if present
        phone = phone.replace("'", "").replace('"', '')
        
        # Handle different formats
        if phone.startswith('+61'):
            # Already in correct format, validate length
            if len(phone) == 12 or len(phone) == 13:
                return phone
        elif phone.startswith('61') and len(phone) >= 11:
            # Missing + prefix
            return '+' + phone
        elif phone.startswith('0') and len(phone) == 10:
            # Australian mobile starting with 0
            return '+61' + phone[1:]
        elif phone.startswith('+1'):
            # US number, keep as is
            return phone
        elif len(phone) == 9 and phone.startswith('4'):
            # Mobile without country code or leading 0
            return '+61' + phone
        elif len(phone) == 8:
            # Landline without area code - flag for manual review
            logger.warning(f"Incomplete phone number requires manual review: {phone}")
            return None
        
        # If we can't standardize, log for manual review
        logger.warning(f"Unable to standardize phone number: {phone}")
        return None
    
    def validate_email(self, email: str) -> Tuple[bool, str]:
        """
        Validate email addresses and clean them.
        Returns (is_valid, cleaned_email)
        """
        if pd.isna(email) or not email:
            return False, ""
        
        email = str(email).strip().lower()
        
        # Basic email validation regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if re.match(email_pattern, email):
            return True, email
        else:
            logger.warning(f"Invalid email format: {email}")
            return False, email
    
    def detect_customer_duplicates(self, contacts_google: pd.DataFrame, 
                                 contacts_regular: pd.DataFrame,
                                 contact_form: pd.DataFrame) -> Dict:
        """
        Detect duplicate customers across multiple data sources.
        Uses email as primary key with fuzzy matching on names.
        """
        logger.info("Starting duplicate detection analysis...")
        
        duplicates = {
            'email_duplicates': [],
            'name_duplicates': [],
            'merge_candidates': []
        }
        
        # Combine all contact sources
        all_contacts = []
        
        # Process Google contacts
        if not contacts_google.empty:
            google_contacts = contacts_google[['E-mail 1 - Value', 'First Name', 'Last Name']].copy()
            google_contacts['source'] = 'google'
            google_contacts.columns = ['email', 'first_name', 'last_name', 'source']
            all_contacts.append(google_contacts)
        
        # Process Regular contacts  
        if not contacts_regular.empty:
            regular_contacts = contacts_regular[['Email', 'First Name', 'Last Name']].copy()
            regular_contacts['source'] = 'regular'
            regular_contacts.columns = ['email', 'first_name', 'last_name', 'source']
            all_contacts.append(regular_contacts)
        
        # Process Contact form submissions
        if not contact_form.empty:
            form_contacts = contact_form[['Email', 'Name']].copy()
            form_contacts['source'] = 'contact_form'
            # Split name into first/last
            form_contacts[['first_name', 'last_name']] = form_contacts['Name'].str.split(' ', 1, expand=True)
            form_contacts = form_contacts[['Email', 'first_name', 'last_name', 'source']]
            form_contacts.columns = ['email', 'first_name', 'last_name', 'source']
            all_contacts.append(form_contacts)
        
        # Combine all contacts
        combined_contacts = pd.concat(all_contacts, ignore_index=True)
        combined_contacts = combined_contacts.dropna(subset=['email'])
        
        # Find email duplicates
        email_counts = combined_contacts['email'].value_counts()
        duplicate_emails = email_counts[email_counts > 1].index.tolist()
        
        for email in duplicate_emails:
            email_records = combined_contacts[combined_contacts['email'] == email]
            duplicates['email_duplicates'].append({
                'email': email,
                'records': email_records.to_dict('records'),
                'count': len(email_records)
            })
        
        # Find potential name duplicates (same name, different email)
        combined_contacts['full_name'] = (combined_contacts['first_name'].fillna('') + ' ' + 
                                        combined_contacts['last_name'].fillna('')).str.strip()
        
        name_counts = combined_contacts['full_name'].value_counts()
        duplicate_names = name_counts[name_counts > 1].index.tolist()
        
        for name in duplicate_names:
            if name and name != '':
                name_records = combined_contacts[combined_contacts['full_name'] == name]
                unique_emails = name_records['email'].nunique()
                if unique_emails > 1:
                    duplicates['name_duplicates'].append({
                        'name': name,
                        'records': name_records.to_dict('records'),
                        'unique_emails': unique_emails
                    })
        
        self.quality_report['duplicates_found'] = len(duplicate_emails) + len(duplicate_names)
        logger.info(f"Found {len(duplicate_emails)} email duplicates and {len(duplicate_names)} name duplicates")
        
        return duplicates
    
    def reconcile_financial_data(self, revenue_summary: pd.DataFrame, 
                               detailed_invoices: pd.DataFrame) -> Dict:
        """
        Reconcile financial data between revenue summary and detailed invoices.
        """
        logger.info("Starting financial data reconciliation...")
        
        reconciliation = {
            'revenue_summary_total': 0,
            'detailed_invoices_total': 0,
            'discrepancies': [],
            'missing_invoices': [],
            'duplicate_invoices': []
        }
        
        # Calculate revenue summary total
        if not revenue_summary.empty and 'Total' in revenue_summary.columns:
            # Clean currency values
            revenue_summary['Total_Clean'] = revenue_summary['Total'].astype(str).str.replace('A$', '').str.replace('$', '').str.replace(',', '')
            revenue_summary['Total_Clean'] = pd.to_numeric(revenue_summary['Total_Clean'], errors='coerce')
            reconciliation['revenue_summary_total'] = revenue_summary['Total_Clean'].sum()
        
        # Calculate detailed invoices total
        if not detailed_invoices.empty and 'Total' in detailed_invoices.columns:
            # Clean currency values
            detailed_invoices['Total_Clean'] = detailed_invoices['Total'].astype(str).str.replace('A$', '').str.replace('$', '').str.replace(',', '')
            detailed_invoices['Total_Clean'] = pd.to_numeric(detailed_invoices['Total_Clean'], errors='coerce')
            reconciliation['detailed_invoices_total'] = detailed_invoices['Total_Clean'].sum()
        
        # Calculate discrepancy
        discrepancy = abs(reconciliation['revenue_summary_total'] - reconciliation['detailed_invoices_total'])
        reconciliation['discrepancy_amount'] = discrepancy
        
        # Find missing invoices (in one dataset but not the other)
        if not revenue_summary.empty and not detailed_invoices.empty:
            if 'Invoice #' in revenue_summary.columns and 'Invoice number' in detailed_invoices.columns:
                revenue_invoices = set(revenue_summary['Invoice #'].dropna().astype(str))
                detailed_invoice_numbers = set(detailed_invoices['Invoice number'].dropna().astype(str))
                
                missing_in_detailed = revenue_invoices - detailed_invoice_numbers
                missing_in_summary = detailed_invoice_numbers - revenue_invoices
                
                reconciliation['missing_invoices'] = {
                    'missing_in_detailed': list(missing_in_detailed),
                    'missing_in_summary': list(missing_in_summary)
                }
        
        self.quality_report['financial_discrepancies'] = len(reconciliation['discrepancies'])
        logger.info(f"Financial reconciliation complete. Discrepancy: ${discrepancy:.2f}")
        
        return reconciliation
    
    def clean_booking_data(self, bookings: pd.DataFrame) -> pd.DataFrame:
        """
        Clean booking data and flag incomplete records.
        """
        logger.info("Cleaning booking data...")
        
        if bookings.empty:
            return bookings
        
        cleaned_bookings = bookings.copy()
        
        # Flag incomplete records
        cleaned_bookings['incomplete_customer_name'] = (
            cleaned_bookings['First Name'].isna() | 
            cleaned_bookings['Last Name'].isna() |
            (cleaned_bookings['First Name'] == '') |
            (cleaned_bookings['Last Name'] == '')
        )
        
        cleaned_bookings['incomplete_email'] = (
            cleaned_bookings['Email'].isna() | 
            (cleaned_bookings['Email'] == '')
        )
        
        cleaned_bookings['incomplete_phone'] = (
            cleaned_bookings['Phone'].isna() | 
            (cleaned_bookings['Phone'] == '')
        )
        
        # Count incomplete records
        incomplete_names = cleaned_bookings['incomplete_customer_name'].sum()
        incomplete_emails = cleaned_bookings['incomplete_email'].sum()
        incomplete_phones = cleaned_bookings['incomplete_phone'].sum()
        
        logger.info(f"Found {incomplete_names} bookings with incomplete names")
        logger.info(f"Found {incomplete_emails} bookings with incomplete emails")
        logger.info(f"Found {incomplete_phones} bookings with incomplete phones")
        
        return cleaned_bookings
    
    def generate_cleaning_report(self) -> str:
        """
        Generate a comprehensive data cleaning report.
        """
        report = f"""
# Data Cleaning Report - Ocean Soul Sparkles

## Summary
- Duplicates Found: {self.quality_report['duplicates_found']}
- Duplicates Resolved: {self.quality_report['duplicates_resolved']}
- Phone Numbers Standardized: {self.quality_report['phones_standardized']}
- Emails Validated: {self.quality_report['emails_validated']}
- Financial Discrepancies: {self.quality_report['financial_discrepancies']}

## Recommendations
1. Review duplicate customer records for manual merge decisions
2. Validate standardized phone numbers before import
3. Remove invalid email addresses from marketing lists
4. Investigate financial discrepancies before migration

## Next Steps
1. Execute data cleaning transformations
2. Set up staging database with cleaned data
3. Implement validation rules in new system
4. Proceed with Phase 1 migration
"""
        return report

def main():
    """Main execution function for data cleaning."""
    logger.info("Starting Ocean Soul Sparkles data cleaning process...")
    
    cleaner = DataCleaner()
    
    # This would be executed with actual data files
    # For now, we're creating the framework and validation scripts
    
    logger.info("Data cleaning framework ready for implementation")
    
    return cleaner.generate_cleaning_report()

if __name__ == "__main__":
    report = main()
    print(report)
